from app.utils.utils import (
    send_data,
    ResponseModel,
    read_file_content,
    extract_reference_item_list,
    split_markdown_text_by_paragraph,
    split_by_citation_section,
    extract_citations_in_paper_by_sentence,
    reference_valid,
    reference_related,
    insert_before_last_char,
    save_text_to_file,
    FAULT,
    stream_handle_before_send,
    Reference,
    is_markdown_title,
    sanitize_filename,
    fault_normal,
    convert_markdown_to_docx,
    hallucination_combine_citation
    # convert_citations_to_sup
)
from app.utils.enum import ErrorType, ProjectConfigError
import asyncio
from fastapi import APIRouter, Depends, UploadFile, Request, HTTPException, status
from app.models.project_configs import ProjectConfig
from typing import List
from datetime import datetime
from app.api.schemas.project_configs import ProjectConfigStatus, UpdateEstimatedTimeProcessingMode, get_status_order
from app.models.workflow import Workflow, Order, Name, WorkflowCategory
from app.api.deps import get_current_user_from_state
from app.api.routes.workflow import delete_workflow
from app.services.prompts import REMOVE_AI_TRACES_SYSTEM_PROMPT, REMOVE_AI_TRACES_PROMPT
from app.services.llm_service import call_llm
import json
from app.api.repository.auth import is_authorization
from app.api.schemas.project_verify import HallucinationResponse, Status, AITraceResponse, ProjectConfigAITraces
from app.utils.content_manager import ContentManager
from app.core.logging import get_logger
from app.api.repository.project_config import get_one_project_config, update_estimated_time


logger = get_logger(__name__)

router = APIRouter()

content_manager = ContentManager()

async def handle_hallucination_data(
    project_config: ProjectConfig,
    # 要不要对符合标准的参考文献进行内容或者存在性检测
    skip_compare: bool = False
):  
    logger.info(f"项目：{project_config.id}开始幻觉审查")
    starttime = datetime.now()
    logger.info(f"幻觉审查开始时间：{starttime.strftime('%Y-%m-%d %H:%M:%S')}")
    try:    
        model_response = project_config.model
        # 报告内容
        report_content = project_config.manual_modified_report or project_config.ai_generated_report
        result_content = read_file_content(report_content)
        # 将原文分成左侧文本，右侧文本和参考文献标题文本
        origin_data = split_by_citation_section(result_content)
        
        if not origin_data:
            logger.info(f"材料项目{project_config.id}没有参考文献段落{project_config.id}")
            # 保存幻觉审查的报告
            project_folder = f"llm_file/{project_config.id}"
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_name = sanitize_filename(f"hallucination_{project_config.name[:10].replace(' ', '_')}_{timestamp}.txt")
            relative_path = f"{project_folder}/{file_name}"
            
            save_text_to_file(
                content=result_content,
                file_path=relative_path
            )

            project_config.hallucination_generated_time = datetime.now()
            project_config.hallucination_report = relative_path
            await project_config.save()
            content_manager.clear_project(project_id=project_config.id)
            return

        # 将引文内容提取出来
        sentences = extract_citations_in_paper_by_sentence(origin_data.text_before_citation_title)
        references = extract_reference_item_list(origin_data.citation_paragraph_without_title)
        # 这是用来暂存参考文献的数组，后续要基于这个List重新排序基于index属性
        right_text: List[dict] = []
        # 这里放置的是所有格式符合的标准参考文献。
        next_list: List[Reference] = []
        for i, reference in enumerate(references):
            # 格式不正确的文献
            if reference.remark and FAULT.TYPE_ERROR.value in reference.remark:
                content_text = hallucination_combine_citation(
                    url=reference.url,
                    index=reference.index,
                    full_text=reference.full_text,
                    error_list=reference.remark
                )
                right_text.append({
                    "content": content_text,
                    "index": reference.index
                })
                # 如果参考文献不标准，那么在正文引用这个参考文献的地方也要加上参考文献不标准的错误
                for sentence in sentences:
                    nums = sentence.nums
                    if reference.index in nums:
                        sentence.remark = [fault_normal(reference.index, FAULT.TYPE_ERROR)]
            # 格式正确的文献
            else:
                if (skip_compare):
                    continue
                next_list.append(reference)
        async def next_fn(reference: Reference):
            logger.info(f"项目：{project_config.id}正在进行幻觉审查的文献标号：{reference.index}")
            result_text = await reference_valid(
                data=reference,
                api_key=model_response.api_key,
                api_url=model_response.api_url,
                model=model_response.model_name
            )
            # 文献可能不存在
            if result_text == 'No':
                if reference.remark:
                    reference.remark.append(FAULT.FAKE_REFERENCE.value)
                else: 
                    reference.remark=[FAULT.FAKE_REFERENCE.value]
            # 检测内容是否和总结文本一致和将幻觉文献的引用内容打上标记
            for sentence in sentences:
                content = sentence.text
                nums = sentence.nums
                if reference.index in nums:
                    # 有总结内容，也就是文献存在
                    if result_text != 'No':
                        if (skip_compare):
                            break
                        # 判断从大模型对网页结果进行总结得到的文献观点和正文中引述的观点是否相关
                        is_related = await reference_related(
                            content=result_text,
                            expression=content,
                            api_key=model_response.api_key,
                            api_url=model_response.api_url,
                            model=model_response.model_name
                        )
                        # 如果不想关就写入错误
                        if not is_related:
                            if sentence.remark:
                                sentence.remark.append(fault_normal(reference.index,FAULT.OPINION_UNFIT))
                            else:
                                sentence.remark = [fault_normal(reference.index,FAULT.OPINION_UNFIT)]
                    # 文献不存在
                    else:
                        if sentence.remark:
                            sentence.remark.append(fault_normal(reference.index,FAULT.FAKE_REFERENCE))
                        else:
                            sentence.remark = [fault_normal(reference.index,FAULT.FAKE_REFERENCE)]
            # 判断文献是否有被正文引用
            is_referenced = any(reference.index in item.nums for item in sentences)
            if not is_referenced:
                if reference.remark:
                    reference.remark.append(FAULT.NOT_REFERENCED.value)
                else: 
                    reference.remark=[FAULT.NOT_REFERENCED.value]
            text = reference.full_text.rstrip('\n')
            full_text = hallucination_combine_citation(
                url=reference.url,
                index=reference.index,
                full_text=text,
                error_list=reference.remark
            )
            right_text.append({
                "content": full_text,
                "index": reference.index
            })
        # 将给定的一维数组切换成元素个数固定的二维数组
        def chunk_list(lst: List[Reference], chunk_size=5):
            return [lst[i:i+chunk_size] for i in range(0, len(lst), chunk_size)]
        wrapper_list = chunk_list(next_list)
        for item in wrapper_list:
            tasks = [next_fn(item1) for item1 in item]
            await asyncio.gather(*tasks)
        logger.info(f"项目：{project_config.id}幻觉审查的异步协程处理完毕。")
        # 这是参考文献段落里面所有的参考文献的序号组成的List
        all_reference_list = [reference.index for reference in references]
        for sentence in sentences:
            # 判断正文里面的角标是不是在参考文献列表里面有
            for num in sentence.nums:
                if num not in all_reference_list:
                    if sentence.remark:
                        sentence.remark.append(fault_normal(num,FAULT.NOT_IN_REFERENCE))
                    else:
                        sentence.remark = [fault_normal(num,FAULT.NOT_IN_REFERENCE)]
            if sentence.remark:
                remark = ('、').join(sentence.remark)
                # sentence.origin形如【我是一个兵。】
                # new_sentence形如【<span style='text-decoration: underline;text-decoration-color: red;'>我是一个兵</span><span style='color: red'>(文献有错误)</span>。】
                new_sentence = insert_before_last_char(
                    f"<span style='text-decoration: underline;text-decoration-color: red;'>{sentence.origin}",
                    f"</span><span style='color: red'>&#40;{remark}&#41;</span>"
                )
                # 将正文里面引述句子更改为新的带错误提示的句子
                origin_data.text_before_citation_title = origin_data.text_before_citation_title.replace(
                    sentence.origin,
                    new_sentence
                )
        # 将参考文献列表按照index属性重新排序。因为刚刚存在异步操作。导致right_text数组里面的每一项可能序号不对
        right_text = sorted(right_text, key=lambda x: x["index"], reverse=False)
        logger.info(f"项目：{project_config.id}幻觉审查完成")
        logger.info(f"right_text:{right_text}")
        origin_data.citation_paragraph_without_title = "\n\n".join([item["content"] for item in right_text])
        logger.info("right_text后面")
        content_list = [
            origin_data.text_before_citation_title,
            origin_data.citation_title,
            origin_data.citation_paragraph_without_title,
        ]
        # 如果参考文献段落后面还有段落
        if origin_data.paragraph_after_citation:
            content_list.append(origin_data.paragraph_after_citation)
        # 保存幻觉审查的报告
        project_folder = f"llm_file/{project_config.id}"
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        file_name = sanitize_filename(f"hallucination_{project_config.name[:10].replace(' ', '_')}_{timestamp}.txt")
        relative_path = f"{project_folder}/{file_name}"
        
        try:
            logger.info(f"content_list: {content_list}")
            save_text_to_file(
                content=("\n\n").join(content_list),
                file_path=relative_path
            )
        except Exception as e:
             logger.error("save_text_to_file这里报错了")
        logger.info(f"项目：{project_config.id}幻觉审查的文件已经写进去了。")
        endtime = datetime.now()
        logger.info(f"幻觉审查结束时间: {endtime.strftime('%Y-%m-%d %H:%M:%S')}，总共用时: {(endtime - starttime).total_seconds()}秒")
        project_config.hallucination_generated_time = datetime.now()
        project_config.hallucination_report = relative_path
        await project_config.save()
        content_manager.clear_project(project_id=project_config.id)
    except Exception as e:
        logger.error(f"项目：{project_config.id}去幻觉的异步处理流程报错：{str(e)}")
        content_manager.clear_project(project_id=project_config.id)

@router.get("/{project_id}/hallucination/report", response_model=ResponseModel[str], summary="幻觉审查报告获取接口")
async def hallucination_report(
    project_id: str
):
    """幻觉审查报告获取接口"""
    try:
        project_config = await get_one_project_config(project_id=project_id)
        if project_config.status == ProjectConfigStatus.REMOVE_HALLUCINATING.value and not project_config.hallucination_report:
            return send_data(False, None, ProjectConfigError.NO_HALLUCINATING_REPORT)
        if get_status_order(project_config.status) < get_status_order(ProjectConfigStatus.REMOVE_HALLUCINATING.value):
            return send_data(False, None, ProjectConfigError.STATUS_ERROR)
        if project_config.hallucination_report:
            content = read_file_content(project_config.hallucination_report)
            content = stream_handle_before_send(content)
            return send_data(True, content)
    except Exception as e:
        return send_data(False, None, str(e))
       

@router.post("/{project_id}/hallucination", response_model=ResponseModel[HallucinationResponse], summary="幻觉审查接口")
async def hallucination(
    project_id: str
):
    """幻觉审查接口"""
    project_config = await ProjectConfig.get_or_none(id=project_id, is_deleted=0).prefetch_related("model")
    if not project_config:
        return send_data(False, None, "项目不存在")
    # 如果project处于幻觉审查中且已经存在幻觉审查报告了。
    last_result = ""
    
    if project_config.hallucination_report:
        try:
            last_result = stream_handle_before_send(read_file_content(
                file_path=project_config.hallucination_report
            ))
        except:
            logger.info("幻觉审查文件不存在")
    logger.info(f"项目：{project_config.id}的状态是{project_config.status}")
    if (project_config.status == ProjectConfigStatus.REMOVE_HALLUCINATING
        and last_result
    ):  
        content_manager.clear_project(project_id=project_config.id)
        return send_data(True, HallucinationResponse.model_validate({
            "status": Status.COMPLETED.value,
            "data": last_result
        }))
    if not project_config.model:
        return send_data(False, None, "项目没有配置模型")
    content = content_manager.get_project_content(project_id=project_config.id)

   
    if content:
        return send_data(True, HallucinationResponse.model_validate({
            "status": Status.PENDING.value,
            "estimated_time": project_config.estimated_time,
            "data": None
        }))
    else:
         # 重新计算预估时间
        try:
            estimated_time = await update_estimated_time(project_config, UpdateEstimatedTimeProcessingMode.HALLUCINATION_CHECK)
            project_config.estimated_time = estimated_time
        except Exception as e:
            logger.error(f"项目：{project_config.id}幻觉审查的预估时间重新计算失败: {str(e)}")
        project_config.status = ProjectConfigStatus.REMOVE_HALLUCINATING.value
        project_config.updated_at = datetime.now()
        await project_config.save()
        taskInstance = asyncio.create_task(handle_hallucination_data(project_config=project_config))
        content_manager.add_asyncio(
            project_id=project_config.id,
            asyncioInstance=taskInstance
        )
        return send_data(True, HallucinationResponse.model_validate({
            "status": Status.PENDING.value,
            "estimated_time": project_config.estimated_time,
            "data": None
        }))
async def handle_ai_traces(project_config: ProjectConfig):
    model_config = project_config.model
    api_key = model_config.api_key
    api_url = model_config.api_url
    model = model_config.model_name
    # 获取报告内容（优先使用人工修改的报告）
    report_content = project_config.manual_modified_report or project_config.ai_generated_report
    # print(sections[0])
    # return False
    result: List[ProjectConfigAITraces] = []
    try:
        # 读取报告文件内容
        report_text = read_file_content(report_content)
        sections = split_markdown_text_by_paragraph(report_text)
        # left_list:List[ProjectConfigAITraces] = []
        for i,section in enumerate(sections):
            # 纯标题不去AI痕迹
            if is_markdown_title(section):
                result.append(ProjectConfigAITraces(
                    seriesNum=i,
                    original=section,
                    modified=section
                ))
            else:
                messages = [
                    {"role": "system", "content": REMOVE_AI_TRACES_SYSTEM_PROMPT},
                    {"role": "user", "content": REMOVE_AI_TRACES_PROMPT.format(content=section)}
                ]
                # modified_content = section
                modified_content = await call_llm(
                    messages=messages,
                    apiKey=api_key,
                    apiUrl=api_url,
                    model=model,
                    stream=False,
                    flag="去AI痕迹"
                )
                logger.info(f"原文内容：{section[0:500]}")
                # data.modified=stream_handle_before_send(modified_content)
                result.append(
                    ProjectConfigAITraces(
                        seriesNum=i,
                        original=section,
                        modified=modified_content
                    )
                )
                # left_list.append(ProjectConfigAITraces(
                #     seriesNum=i,
                #     original=section,
                #     modified=""
                # ))
        # def chunk_list(lst: List[ProjectConfigAITraces], chunk_size=5):
        #     return [lst[i:i+chunk_size] for i in range(0, len(lst), chunk_size)]
        # # 将还没幻觉处理的文本切成二位数组
        # wrapper_list = chunk_list(left_list)
        # async def call_fn(data: ProjectConfigAITraces):
        #     messages = [
        #         {"role": "system", "content": REMOVE_AI_TRACES_SYSTEM_PROMPT},
        #         {"role": "user", "content": REMOVE_AI_TRACES_PROMPT.format(content=data.original)}
        #     ]
        #     # modified_content = section
        #     modified_content = await call_llm(
        #         messages=messages,
        #         apiKey=api_key,
        #         apiUrl=api_url,
        #         model=model,
        #         stream=False,
        #         flag="去AI痕迹"
        #     )
        #     logger.info(f"原文内容：{data.original[0:500]}")
        #     data.modified=stream_handle_before_send(modified_content)
        #     result.append(data)
        # for item in wrapper_list:
        #     tasks = [call_fn(item1) for item1 in item]
        #     await asyncio.gather(*tasks)
            # logger.info(f"AI去痕迹的进度：{i}/{len(sections)}")
    except Exception as e:
        logger.info(f"AI去痕迹失败：{str(e)}")
        return send_data(False, None, f"报告内容读取失败: {str(e)}")

    final_list = sorted(result, key=lambda x: x.seriesNum, reverse=False)
    result_content = json.dumps([item.model_dump() for item in final_list])
    # 保存去AI痕迹的报告
    project_folder = f"llm_file/{project_config.id}"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    file_name = sanitize_filename(f"remove_ai_trace_{project_config.name[:10].replace(' ', '_')}_{timestamp}.txt")
    relative_path = f"{project_folder}/{file_name}"
    
    save_text_to_file(
        content=result_content,
        file_path=relative_path
    )
    # 更新项目状态
    project_config.ai_trace_generated_time = datetime.now()
    project_config.ai_trace_report = relative_path
    await project_config.save()
    content_manager.clear_project(project_id=project_config.id)

@router.post("/{project_id}/traces", response_model=ResponseModel[AITraceResponse], summary="AI去痕迹接口")
async def remove_ai_traces(project_id: str):
    """
    AI去痕迹接口
    
    处理项目报告中的AI特征，使其更接近人工写作风格
    
    Args:
        project_id: 项目ID
        
    Returns:
        处理后的报告内容
    """
    project_config = await ProjectConfig.filter(id=project_id, is_deleted=0).prefetch_related("model").first()
    if not project_config:
        return send_data(False, None, "项目不存在")
    if project_config.status == ProjectConfigStatus.REMOVE_AI_TRACING and project_config.ai_trace_report:
        try:
            result_data = read_file_content(project_config.ai_trace_report)
            list_data = json.loads(result_data)
            content_manager.clear_project(project_id=project_config.id)
            return send_data(True, AITraceResponse.model_validate({
                "data": list_data,
                "status": Status.COMPLETED.value
            }))
        except:
            project_config.status = ProjectConfigStatus.REMOVE_AI_TRACING.value
            project_config.updated_at = datetime.now()
            project_config.ai_trace_report = None
            await project_config.save()
            # remove_ai_traces(project_id)
            return send_data(False, None, "去AI痕迹的报告解析失败请刷新重试")
    model_config = project_config.model
    if not model_config:
        return send_data(False, None, "报告的模型配置不存在")
    content = content_manager.get_project_content(project_id=project_config.id)
    if content and content.asyncioInstance:
        return send_data(True, AITraceResponse.model_validate({
            "status": Status.PENDING.value,
            "data": None,
            "estimated_time": project_config.estimated_time
        }))
    else:
        # 重新计算预估时间
        try:
            estimated_time = await update_estimated_time(project_config, UpdateEstimatedTimeProcessingMode.AI_TRACE_REMOVAL)
            project_config.estimated_time = estimated_time
        except Exception as e:
            logger.error(f"项目：{project_config.id}去AI痕迹的预估时间重新计算失败: {str(e)}")
            # 计算失败时保持原有预估时间不变
        project_config.status = ProjectConfigStatus.REMOVE_AI_TRACING.value
        project_config.updated_at = datetime.now()
        await project_config.save()
        taskInstance = asyncio.create_task(handle_ai_traces(project_config=project_config))
        content_manager.add_asyncio(
            project_id=project_config.id,
            asyncioInstance=taskInstance
        )
        return send_data(True, AITraceResponse.model_validate({
            "data": None,
            "status": Status.PENDING.value,
            "estimated_time": project_config.estimated_time
        }))

@router.post("/{project_id}/traces/complete", response_model=ResponseModel[bool], summary="完成AI去痕迹")
async def complete_ai_traces(
    project_id: str,
    request: Request
):
    """
    完成AI去痕迹
    
    Args:
        project_id: 项目ID
        
    Returns:
        bool: 是否完成
    """
    current_user = get_current_user_from_state(request)
    try:
        project_config = await ProjectConfig.get_or_none(id=project_id, is_deleted=0)
        if not project_config:
            return send_data(False, None, "项目不存在")
        if project_config.status != ProjectConfigStatus.REMOVE_AI_TRACING.value:
            return send_data(False, None, "报告的状态不是去AI痕迹中")
        project_config.status = ProjectConfigStatus.REMOVE_AI_TRACED.value
        # 更新项目状态
        project_config.updated_at = datetime.now()
        await project_config.save()
        await delete_workflow(project_id, Name.AI_TRACE_REMOVAL.name)
        await Workflow.create(
            project_config_id=project_id,
            name=Name.AI_TRACE_REMOVAL.name,
            content=project_config.manual_modified_report or project_config.ai_generated_report,
            content_remark=project_config.ai_trace_report,
            operator_id=current_user.id,
            created_at=datetime.now(),
            order=Order.AI_TRACE_REMOVAL.value,
            category=WorkflowCategory.OTHER.value,
        )
        instance = content_manager.get_project_content(
            project_id=project_config.id
        )
        if instance:
            instance.asyncioInstance.cancel()
            content_manager.clear_project(project_id=project_config.id)
        return send_data(True, True)
    except Exception as e:
        return send_data(False, None, f"完成AI去痕迹失败: {str(e)}")

@router.post("/{project_id}/hallucination/complete", response_model=ResponseModel[bool], summary="完成幻觉审查")
async def complete_hallucination(
    project_id: str,
    request: Request
):
    """
    完成幻觉审查
    
    Args:
        project_id: 项目ID
        
    Returns:
        bool: 是否完成
    """
    current_user = get_current_user_from_state(request)
    try:
        project_config = await ProjectConfig.get_or_none(id=project_id, is_deleted=0)
        if not project_config:
            return send_data(False, None, "项目不存在")
        if project_config.status != ProjectConfigStatus.REMOVE_HALLUCINATING.value:
            return send_data(False, None, "报告的状态不是幻觉审查中")    
        # 更新项目状态
        project_config.status = ProjectConfigStatus.REMOVE_HALLUCINATED.value 
        project_config.updated_at = datetime.now()
        await project_config.save()
        await delete_workflow(project_id, Name.ILLUSION_REVIEW.name)
        await Workflow.create(
            project_config_id=project_id,
            name=Name.ILLUSION_REVIEW.name,
            content=project_config.manual_modified_report or project_config.ai_generated_report,
            content_remark=project_config.hallucination_report,
            operator_id=current_user.id,
            created_at=datetime.now(),
            order=Order.ILLUSION_REVIEW.value,
            category=WorkflowCategory.OTHER.value,
        )
        instance = content_manager.get_project_content(
            project_id=project_config.id
        )
        if instance:
            instance.asyncioInstance.cancel()
            content_manager.clear_project(project_id=project_config.id)
        return send_data(True, True)
    except Exception as e:
        return send_data(False, None, f"完成幻觉审查失败: {str(e)}")
    
# @router.post("/literature", response_model=ResponseModel[str], summary="返回文献情况")
# async def validate(
#     file: UploadFile
# ):  
#     contents = await file.read()  # contents 是 bytes 类型
#     filename = file.filename
#     route = f"llm_file/literature/{filename}"
#     async def asyncFn():    
#         text = contents.decode("utf-8")
#         list_data: List[str] = []
#         for index, item in enumerate(extract_reference_item_list(text)):
#             print(f"文件：{filename}的进度{index + 1}/{len(extract_reference_item_list(text))}")
#             try:
#                 result = await reference_literature_valid(
#                     reference=item
#                 )
#                 if result['reason'] != 'Yes':
#                     list_data.append(f"[{index + 1}]{result['text']}")
#             except Exception as e:
#                 list_data.append(f"[{index + 1}]{text}")
#                 continue
#             save_text_to_file(("\n").join(list_data), route)
#         save_text_to_file(("\n").join(list_data) + "\n已完成", route)
#     try:
#         result = read_file_content(route)
#         return send_data(True, result)
#     except Exception as e:
#         save_text_to_file("", route)
#         asyncio.create_task(asyncFn())
#     return send_data(True, "还在处理，你先等着")
#     # return send_data(True, list_data)
# async def reference_literature_valid(
#     reference: Reference,
#     limit: int = 2
# ) -> dict:
#     response = "No"
#     # 有标题有页码有卷号
#     if reference.title and reference.pages and reference.volume:
#         urls = await perform_serpapi_search(
#             query=reference.title,
#             limit=limit
#         )
#         for url in urls:
#             result_content = await fetch_webpage_text_async(url)
#             page_content = result_content[0:20000]
#             # 做一个初步过滤
#             if (reference.pages in page_content
#                 and reference.volume in page_content
#                 and reference.year in page_content
#             ):

#                 # 调用LLM
#                 messages = [
#                     {
#                         "role": "system",
#                         "content": "你是一个帮助用户核查学术参考文献的语言模型专家。"
#                     },
#                     {
#                         "role": "user",
#                         "content": f"""
#                             请根据下方网页文本，判断给出的参考文献信息是否准确、真实地反映网页中提到的文献信息。
#                             请检查参考文献中的**作者、标题、出版年份、期刊、期号、卷号、DOI（如有）**等是否与网页内容一致。
#                             如果信息一致，请返回"Yes"；
#                             如果存在虚构、误引、信息不一致或网页中未提到该文献，请返回 "No"。
#                             网页文本：```{page_content}```
#                             参考文献：```{reference.full_text}```
#                         """
#                     }
#                 ]
#                 response = await call_llm(
#                     messages=messages,
#                     flag="提取网页内容和参考文献进行比对",
#                     apiKey="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",
#                     apiUrl="https://openrouter.ai/api/v1/chat/completions",
#                     model="anthropic/claude-3.7-sonnet:thinking"
#                 )
#                 if response == 'Yes':
#                     break
#             else:
#                 continue
#     return {
#         "text": reference.full_text,
#         "reason": response
#     }
@router.get("/{project_id}/download", summary="下载幻觉审查报告")
async def download_file(
    project_id: str,
    request: Request
):
    current_user =  get_current_user_from_state(request)
    if not project_id:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="项目ID不能为空"
        )
    project = await ProjectConfig.filter(id=project_id,is_deleted=False).prefetch_related("user", "user__organization").first()
    if not project:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="项目不存在"
        )
    authed = is_authorization(current_user, project.user)
    if not authed:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ErrorType.FORBIDDEN.value
        )
    if not project.hallucination_report:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="幻觉审查报告不存在"
        )
    try:
        return convert_markdown_to_docx(project.hallucination_report)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
    
    